import openpyxl
from openpyxl import Workbook
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.utils import get_column_letter

# 住宿标准表数据（部分示例，可自行扩展）
hotel_standards = [
    ["福建", "福州", 480, 380, "", "", ""],
    ["福建", "厦门", 650, 520, "", "", ""],
    ["福建", "泉州", 480, 380, "", "", ""],
    ["福建", "平潭综合实验区", 480, 380, "", "", ""],
    ["福建", "其他地区", 480, 350, "", "", ""],
    ["北京", "全市", 980, 750, "", "", ""],
    ["上海", "全市", 900, 750, "", "", ""],
    ["西藏", "拉萨市", 650, 460, "6-9月", 980, 690],
    ["新疆", "乌鲁木齐市", 620, 460, "", "", ""],
    ["青海", "西宁市", 650, 460, "6-9月", 980, 690],
]

wb = Workbook()
ws = wb.active
ws.title = "差旅计算"

# 主表表头
title = [
    "姓名", "职务级别", "出差省", "出差市", "出差天数", "交通工具", "交通等级", "住宿星级", "是否集团饭店", "是否旺季", "伙食自行解决", "市内交通自行解决", "城市间交通费", "住宿费", "伙食补助费", "市内交通费", "总计"
]
ws.append(title)

# 下拉选项
position_list = ["领导班子", "其他人员"]
vehicle_list = ["火车", "飞机", "轮船", "汽车"]
vehicle_level_list = ["软席", "硬席", "一等座", "二等座", "经济舱"]
hotel_level_list = ["五星级", "四星级", "三星级", "经济型"]
yesno_list = ["是", "否"]

# 写入下拉数据到隐藏sheet
ws2 = wb.create_sheet("下拉选项")
ws2.append(["职务级别"] + position_list)
ws2.append(["交通工具"] + vehicle_list)
ws2.append(["交通等级"] + vehicle_level_list)
ws2.append(["住宿星级"] + hotel_level_list)
ws2.append(["是/否"] + yesno_list)

# 住宿标准表
ws3 = wb.create_sheet("住宿标准表")
ws3.append(["省", "市/区", "领导班子", "其他人员", "旺季时间", "旺季领导班子", "旺季其他人员"])
for row in hotel_standards:
    ws3.append(row)

# 设置下拉菜单
# 职务级别
pos_dv = DataValidation(type="list", formula1='下拉选项!$B$1:$C$1', allow_blank=True)
ws.add_data_validation(pos_dv)
pos_dv.add(ws["B2:B101"])
# 交通工具
veh_dv = DataValidation(type="list", formula1='下拉选项!$B$2:$E$2', allow_blank=True)
ws.add_data_validation(veh_dv)
veh_dv.add(ws["F2:F101"])
# 交通等级
veh_lv_dv = DataValidation(type="list", formula1='下拉选项!$B$3:$F$3', allow_blank=True)
ws.add_data_validation(veh_lv_dv)
veh_lv_dv.add(ws["G2:G101"])
# 住宿星级
hotel_lv_dv = DataValidation(type="list", formula1='下拉选项!$B$4:$E$4', allow_blank=True)
ws.add_data_validation(hotel_lv_dv)
hotel_lv_dv.add(ws["H2:H101"])
# 是/否
yesno_dv = DataValidation(type="list", formula1='下拉选项!$B$5:$C$5', allow_blank=True)
ws.add_data_validation(yesno_dv)
yesno_dv.add(ws["I2:K101"])
yesno_dv.add(ws["J2:J101"])
yesno_dv.add(ws["K2:K101"])
yesno_dv.add(ws["L2:L101"])

# 公式
for row in range(2, 102):
    # 住宿费公式（示例：需根据实际省市和职务级别查表，可用VLOOKUP实现，Excel中需手动调整）
    ws[f'N{row}'] = f''  # 预留公式位
    # 伙食补助费
    ws[f'O{row}'] = f'=IF(OR(C{row}="西藏",C{row}="青海",C{row}="新疆"),120,100)*E{row}'
    # 市内交通费
    ws[f'P{row}'] = f'=80*E{row}'
    # 总计
    ws[f'Q{row}'] = f'=SUM(M{row}:P{row})'

# 设置列宽
for i, w in enumerate([10, 12, 10, 10, 10, 10, 10, 10, 12, 10, 12, 16, 12, 10, 12, 12, 10], 1):
    ws.column_dimensions[get_column_letter(i)].width = w

# 隐藏下拉选项和住宿标准表sheet
ws2.sheet_state = 'hidden'
ws3.sheet_state = 'hidden'

wb.save('差旅自动计算模板.xlsx')
print('Excel模板已生成：差旅自动计算模板.xlsx') 