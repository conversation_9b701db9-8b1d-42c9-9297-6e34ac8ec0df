<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>福建省旅游发展集团差旅费计算表</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .form-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .form-section h3 {
            color: #34495e;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .form-row {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }
        .form-group {
            margin-right: 20px;
            display: flex;
            align-items: center;
        }
        label {
            font-weight: bold;
            margin-right: 10px;
            min-width: 100px;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        input[type="number"], input[type="date"] {
            width: 120px;
        }
        input[type="text"] {
            width: 150px;
        }
        select {
            width: 180px;
        }
        .calculate-btn {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
        }
        .calculate-btn:hover {
            background-color: #2980b9;
        }
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .result-table th, .result-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        .result-table th {
            background-color: #3498db;
            color: white;
        }
        .result-table .total-row {
            background-color: #ecf0f1;
            font-weight: bold;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>福建省旅游发展集团有限公司差旅费计算表</h1>
        
        <!-- 基本信息 -->
        <div class="form-section">
            <h3>基本信息</h3>
            <div class="form-row">
                <div class="form-group">
                    <label>姓名：</label>
                    <input type="text" id="name" placeholder="请输入姓名">
                </div>
                <div class="form-group">
                    <label>职务级别：</label>
                    <select id="position">
                        <option value="leader">集团领导班子成员</option>
                        <option value="subsidiary_leader">二级企业正职负责人</option>
                        <option value="important_subsidiary">省国资委认定重要子企业正职</option>
                        <option value="special_talent">特殊人才</option>
                        <option value="other">其他人员</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 出差信息 -->
        <div class="form-section">
            <h3>出差信息</h3>
            <div class="form-row">
                <div class="form-group">
                    <label>出差地点：</label>
                    <select id="destination">
                        <option value="">请选择出差地点</option>
                        <option value="beijing">北京</option>
                        <option value="shanghai">上海</option>
                        <option value="guangzhou">广州</option>
                        <option value="shenzhen">深圳</option>
                        <option value="hangzhou">杭州</option>
                        <option value="nanjing">南京</option>
                        <option value="xiamen">厦门</option>
                        <option value="fuzhou">福州</option>
                        <option value="other_province">省外其他城市</option>
                        <option value="other_fujian">省内其他城市</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>出差类型：</label>
                    <select id="tripType">
                        <option value="province_out">省外出差</option>
                        <option value="province_in">省内出差</option>
                        <option value="tibet">西藏/青海/新疆</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>开始日期：</label>
                    <input type="date" id="startDate">
                </div>
                <div class="form-group">
                    <label>结束日期：</label>
                    <input type="date" id="endDate">
                </div>
                <div class="form-group">
                    <label>出差天数：</label>
                    <input type="number" id="days" readonly>
                </div>
            </div>
        </div>

        <!-- 交通费用 -->
        <div class="form-section">
            <h3>交通费用</h3>
            <div class="form-row">
                <div class="form-group">
                    <label>往返交通费：</label>
                    <input type="number" id="transportFee" placeholder="请输入实际交通费" step="0.01">
                </div>
                <div class="form-group">
                    <label>交通工具：</label>
                    <select id="transportType">
                        <option value="plane">飞机</option>
                        <option value="train_soft">火车软席/高铁一等座</option>
                        <option value="train_hard">火车硬席/高铁二等座</option>
                        <option value="ship">轮船</option>
                        <option value="bus">公路交通工具</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 住宿费用 -->
        <div class="form-section">
            <h3>住宿费用</h3>
            <div class="form-row">
                <div class="form-group">
                    <label>住宿费总额：</label>
                    <input type="number" id="accommodationFee" placeholder="请输入实际住宿费" step="0.01">
                </div>
                <div class="form-group">
                    <label>住宿标准：</label>
                    <input type="number" id="accommodationStandard" readonly>
                </div>
            </div>
        </div>

        <button class="calculate-btn" onclick="calculateExpenses()">计算差旅费</button>

        <!-- 计算结果 -->
        <div class="form-section">
            <h3>费用计算结果</h3>
            <table class="result-table">
                <thead>
                    <tr>
                        <th>费用项目</th>
                        <th>标准/天</th>
                        <th>天数</th>
                        <th>应发金额</th>
                        <th>实际金额</th>
                        <th>报销金额</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>城市间交通费</td>
                        <td>-</td>
                        <td>-</td>
                        <td id="transportStandard">-</td>
                        <td id="transportActual">-</td>
                        <td id="transportReimbursement">-</td>
                        <td id="transportNote">凭据报销</td>
                    </tr>
                    <tr>
                        <td>住宿费</td>
                        <td id="accommodationStandardPerDay">-</td>
                        <td id="accommodationDays">-</td>
                        <td id="accommodationTotal">-</td>
                        <td id="accommodationActualTotal">-</td>
                        <td id="accommodationReimbursementTotal">-</td>
                        <td id="accommodationNoteTotal">限额内据实报销</td>
                    </tr>
                    <tr>
                        <td>伙食补助费</td>
                        <td id="mealStandard">-</td>
                        <td id="mealDays">-</td>
                        <td id="mealTotal">-</td>
                        <td>-</td>
                        <td id="mealReimbursement">-</td>
                        <td>包干使用</td>
                    </tr>
                    <tr>
                        <td>市内交通费</td>
                        <td>80</td>
                        <td id="localTransportDays">-</td>
                        <td id="localTransportTotal">-</td>
                        <td>-</td>
                        <td id="localTransportReimbursement">-</td>
                        <td>包干使用</td>
                    </tr>
                    <tr class="total-row">
                        <td>合计</td>
                        <td>-</td>
                        <td>-</td>
                        <td id="grandTotal">-</td>
                        <td>-</td>
                        <td id="grandReimbursement">-</td>
                        <td>-</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="note">
            <h4>重要说明：</h4>
            <ul>
                <li>住宿费标准根据出差地点和职务级别自动确定</li>
                <li>伙食补助费：省内100元/天，省外100元/天（西藏、青海、新疆120元/天）</li>
                <li>市内交通费：80元/天包干使用</li>
                <li>超标准部分个人自理</li>
                <li>具体标准请参考《福建省旅游发展集团有限公司差旅费管理实施细则》</li>
            </ul>
        </div>
    </div>

    <script>
        // 住宿费标准表（简化版，主要城市）
        const accommodationStandards = {
            'beijing': { leader: 980, other: 750 },
            'shanghai': { leader: 900, other: 750 },
            'guangzhou': { leader: 830, other: 680 },
            'shenzhen': { leader: 830, other: 680 },
            'hangzhou': { leader: 650, other: 520 },
            'nanjing': { leader: 640, other: 490 },
            'xiamen': { leader: 650, other: 520 },
            'fuzhou': { leader: 480, other: 380 },
            'other_province': { leader: 600, other: 450 },
            'other_fujian': { leader: 480, other: 350 }
        };

        // 计算出差天数
        function calculateDays() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const timeDiff = end.getTime() - start.getTime();
                const dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // 包含起始日
                document.getElementById('days').value = dayDiff > 0 ? dayDiff : 0;
                updateAccommodationStandard();
            }
        }

        // 更新住宿标准
        function updateAccommodationStandard() {
            const destination = document.getElementById('destination').value;
            const position = document.getElementById('position').value;
            
            if (destination && accommodationStandards[destination]) {
                const isLeader = position === 'leader' || position === 'subsidiary_leader' || 
                               position === 'important_subsidiary' || position === 'special_talent';
                const standard = isLeader ? accommodationStandards[destination].leader : 
                                          accommodationStandards[destination].other;
                document.getElementById('accommodationStandard').value = standard;
            }
        }

        // 计算差旅费
        function calculateExpenses() {
            const days = parseInt(document.getElementById('days').value) || 0;
            const destination = document.getElementById('destination').value;
            const position = document.getElementById('position').value;
            const tripType = document.getElementById('tripType').value;
            const transportFee = parseFloat(document.getElementById('transportFee').value) || 0;
            const accommodationFee = parseFloat(document.getElementById('accommodationFee').value) || 0;

            if (days === 0) {
                alert('请先选择出差日期');
                return;
            }

            // 住宿费计算
            const isLeader = position === 'leader' || position === 'subsidiary_leader' || 
                           position === 'important_subsidiary' || position === 'special_talent';
            let accommodationStandard = 0;
            if (destination && accommodationStandards[destination]) {
                accommodationStandard = isLeader ? accommodationStandards[destination].leader : 
                                                 accommodationStandards[destination].other;
            }
            
            const accommodationLimit = accommodationStandard * days;
            const accommodationReimbursement = Math.min(accommodationFee, accommodationLimit);

            // 伙食补助费计算
            let mealStandard = 100; // 默认100元/天
            if (tripType === 'tibet') {
                mealStandard = 120; // 西藏、青海、新疆120元/天
            }
            const mealTotal = mealStandard * days;

            // 市内交通费计算
            const localTransportStandard = 80;
            const localTransportTotal = localTransportStandard * days;

            // 更新表格
            document.getElementById('transportStandard').textContent = '-';
            document.getElementById('transportActual').textContent = transportFee.toFixed(2);
            document.getElementById('transportReimbursement').textContent = transportFee.toFixed(2);

            document.getElementById('accommodationStandardPerDay').textContent = accommodationStandard;
            document.getElementById('accommodationDays').textContent = days;
            document.getElementById('accommodationTotal').textContent = accommodationLimit.toFixed(2);
            document.getElementById('accommodationActualTotal').textContent = accommodationFee.toFixed(2);
            document.getElementById('accommodationReimbursementTotal').textContent = accommodationReimbursement.toFixed(2);

            document.getElementById('mealStandard').textContent = mealStandard;
            document.getElementById('mealDays').textContent = days;
            document.getElementById('mealTotal').textContent = mealTotal.toFixed(2);
            document.getElementById('mealReimbursement').textContent = mealTotal.toFixed(2);

            document.getElementById('localTransportDays').textContent = days;
            document.getElementById('localTransportTotal').textContent = localTransportTotal.toFixed(2);
            document.getElementById('localTransportReimbursement').textContent = localTransportTotal.toFixed(2);

            // 计算总计
            const grandTotal = accommodationLimit + mealTotal + localTransportTotal;
            const grandReimbursement = transportFee + accommodationReimbursement + mealTotal + localTransportTotal;

            document.getElementById('grandTotal').textContent = grandTotal.toFixed(2);
            document.getElementById('grandReimbursement').textContent = grandReimbursement.toFixed(2);

            // 超标提醒
            if (accommodationFee > accommodationLimit) {
                document.getElementById('accommodationNoteTotal').textContent = 
                    `限额内据实报销，超标${(accommodationFee - accommodationLimit).toFixed(2)}元个人自理`;
                document.getElementById('accommodationNoteTotal').style.color = 'red';
            }
        }

        // 事件监听
        document.getElementById('startDate').addEventListener('change', calculateDays);
        document.getElementById('endDate').addEventListener('change', calculateDays);
        document.getElementById('destination').addEventListener('change', updateAccommodationStandard);
        document.getElementById('position').addEventListener('change', updateAccommodationStandard);
    </script>
</body>
</html>
